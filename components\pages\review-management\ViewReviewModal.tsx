'use client';

import { useState, useEffect } from 'react';

interface Note {
    id: number;
    note: string;
    createdAt: string;
    adminName: string;
}

interface HistoryEntry {
    id: number;
    action: string;
    previousStatus: number;
    newStatus: number;
    previousStatusName: string;
    newStatusName: string;
    notes: string | null;
    createdAt: string;
    adminName: string;
}

interface ReviewWithHistory {
    id: number;
    reviewText: string;
    rating: number;
    statusName: string;
    created_at: string;
    reviewerFirstName?: string;
    reviewerLastName?: string;
    reviewerEmail?: string;
    revieweeFirstName?: string;
    revieweeLastName?: string;
    revieweeEmail?: string;
    revieweeType?: string;
    flagged?: boolean;
    comment?: string; // Fallback for legacy data
    notes: Note[];
    history: HistoryEntry[];
}

interface ViewReviewModalProps {
    review: ReviewWithHistory | null;
    onClose: () => void;
    onDelete?: (id: number) => void;
    onApprove?: (id: number) => void;
    onReject?: (id: number) => void;
    onHide?: (id: number) => void;
}

export default function ViewReviewModal({ review, onClose, onDelete, onApprove, onReject, onHide }: ViewReviewModalProps) {
    if (!review) return null;

    // Combine and sort all history items chronologically
    const getCombinedHistory = () => {
        const combined: Array<{
            id: number;
            type: 'note' | 'history';
            createdAt: string;
            adminName: string;
            content: string;
            action?: string;
            previousStatusName?: string;
            newStatusName?: string;
        }> = [];

        // Add notes
        if (review.notes) {
            review.notes.forEach((note) => {
                combined.push({
                    id: note.id,
                    type: 'note',
                    createdAt: note.createdAt,
                    adminName: note.adminName,
                    content: note.note,
                });
            });
        }

        // Add history entries
        if (review.history) {
            review.history.forEach((historyEntry) => {
                combined.push({
                    id: historyEntry.id,
                    type: 'history',
                    createdAt: historyEntry.createdAt,
                    adminName: historyEntry.adminName,
                    content:
                        historyEntry.action === 'status_update'
                            ? `Status changed from ${historyEntry.previousStatusName} to ${historyEntry.newStatusName}`
                            : historyEntry.action === 'restore'
                            ? `Review restored from ${historyEntry.previousStatusName} to ${historyEntry.newStatusName}`
                            : historyEntry.notes || `Action: ${historyEntry.action}`,
                    action: historyEntry.action,
                    previousStatusName: historyEntry.previousStatusName,
                    newStatusName: historyEntry.newStatusName,
                });
            });
        }

        // Sort by creation date (newest first)
        return combined.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    };

    const combinedHistory = getCombinedHistory();

    const getStatusBadge = (status: string) => {
        const statusClasses = {
            Published: 'bg-[#2d2d2e] text-white',
            Pending: 'bg-[#fff3cd] text-[#856404] border border-[#ffeaa7]',
            Rejected: 'bg-red-500 text-white',
            Archived: 'bg-gray-500 text-white',
        };

        return <span className={`rounded-full px-3 py-1 text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800'}`}>{status}</span>;
    };

    const renderStars = (rating: number) => {
        return (
            <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                    <svg key={star} className={`h-4 w-4 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                ))}
                <span className="ml-1 text-sm text-gray-600">{rating}/5</span>
            </div>
        );
    };

    const reviewerName = review.reviewerFirstName && review.reviewerLastName ? `${review.reviewerFirstName} ${review.reviewerLastName}` : review.reviewerEmail || 'Unknown';

    const revieweeName = review.revieweeFirstName && review.revieweeLastName ? `${review.revieweeFirstName} ${review.revieweeLastName}` : review.revieweeEmail || 'Unknown';

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return (
            date.toLocaleDateString('en-US', {
                month: '2-digit',
                day: '2-digit',
                year: 'numeric',
            }) +
            ', ' +
            date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
            })
        );
    };

    return (
            <div className="max-h-[90vh]">
                {/* Header */}
                <div className="mb-6 flex items-start justify-between">
                    <div className="flex items-start gap-3">{getStatusBadge(review.statusName)}</div>
                    <button onClick={onClose} className="p-1 text-gray-400 hover:text-gray-600" title="Close modal">
                        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div className="border-b border-gray-200 pl-2 pb-6">
                    {/* Title */}
                    <h2 className="mb-2 text-xl font-semibold text-gray-900">Review by {reviewerName}</h2>

                        {/* Date and Rating */}
                        <p className="mb-3 text-sm text-gray-500">{formatDate(review.created_at)}</p>

                        <div className="mb-6">{renderStars(review.rating)}</div>
                    </div>
                    <div className="p-3">
                        {/* Reviewer and Target Info */}
                        <div className="mb-6 grid grid-cols-2 gap-6">
                            {/* Reviewer */}
                            <div>
                                <h3 className="mb-2 text-lg font-semibold text-gray-900">Reviewer</h3>
                                <div className="text-sm font-medium text-gray-900">{reviewerName}</div>
                                <div className="text-sm text-gray-500">{review.reviewerEmail}</div>
                            </div>

                            {/* For */}
                            <div>
                                <h3 className="mb-2  text-lg font-semibold text-gray-900">For</h3>
                                <div className="text-sm font-medium text-gray-900">{revieweeName}</div>
                                <div className="text-sm text-gray-500">{review.revieweeType === 'Individual' ? 'Individual' : 'Agency'}</div>
                            </div>
                        </div>

                        {/* Review Comment */}
                        <div className="mb-6">
                            <h3 className="mb-2 text-lg font-semibold text-gray-900">Review comment</h3>
                            <div className="rounded-md bg-gray-50 p-4">
                                <p className="text-sm leading-relaxed text-gray-700">{review.reviewText || review.comment || 'No comment provided.'}</p>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="mb-6 flex gap-3">
                            {review.statusName === 'Pending' && onApprove && (
                                <button onClick={() => onApprove(review.id)} className="rounded bg-green-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-green-600">
                                    Approve
                                </button>
                            )}

                            {onDelete && (
                                <button
                                    onClick={() => onDelete(review.id)}
                                    className="flex items-center gap-2 rounded bg-red-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-red-600"
                                >
                                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                        />
                                    </svg>
                                    Delete
                                </button>
                            )}

                            {onHide && (
                                <button
                                    onClick={() => onHide(review.id)}
                                    className="flex items-center gap-2 rounded border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50"
                                >
                                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L6.5 6.5m3.378 3.378a3 3 0 013.243-2.878m0 0L16.5 4.5m-3.379 2.878L9.878 9.878m4.242 4.242L16.5 16.5m-2.378-3.378a3 3 0 01-4.243-4.243m0 0L6.5 6.5"
                                        />
                                    </svg>
                                    Hide
                                </button>
                            )}
                        </div>

                        {/* Comment History */}
                        <div>
                            <h3 className="mb-4 text-sm font-semibold text-gray-900">Comment & Activity History ({combinedHistory.length})</h3>

                            <div className="space-y-3">
                                {combinedHistory.length > 0 ? (
                                    combinedHistory.map((item, index) => (
                                        <div key={`${item.type}-${item.id}-${index}`} className="flex items-start gap-3">
                                            <span
                                                className={`inline-flex items-center rounded px-2 py-1 text-xs font-medium ${
                                                    item.type === 'note' ? 'bg-gray-100 text-gray-800' : item.action === 'restore' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                                                }`}
                                            >
                                                {item.type === 'note'
                                                    ? 'Note'
                                                    : item.action === 'status_update'
                                                    ? 'Status Change'
                                                    : item.action === 'restore'
                                                    ? 'Restore'
                                                    : `📋 ${item.action?.charAt(0).toUpperCase()}${item.action?.slice(1)}`}
                                            </span>
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm font-medium text-gray-900">{item.adminName}</span>
                                                    <span className="text-xs text-gray-500">{new Date(item.createdAt).toLocaleString()}</span>
                                                </div>
                                                <p className="mt-1 text-sm text-gray-600">{item.content}</p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="flex items-center justify-center py-6">
                                        <div className="text-center">
                                            <svg className="mx-auto mb-2 h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                />
                                            </svg>
                                            <p className="text-sm text-gray-500">No comment history available</p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

    );
}
